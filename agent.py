import os
import json
from openai import OpenAI
from dotenv import load_dotenv
from tools import MATH_TOOLS, execute_math_tool

# Load environment variables from .env file
load_dotenv()

def run_agent(prompt):
    """Run the agent with a given prompt using LLM"""
    # Initialize OpenAI client with OpenRouter configuration
    client = OpenAI(
        api_key=os.environ["API_KEY"],
        base_url="https://api.openai.com/v1"
    )
    
    messages = [{"role": "user", "content": prompt}]
    
    print(f"🤖 Working on: {prompt}\n")
    
    while True:
        # Call the AI with tools
        response = client.chat.completions.create(
            model=os.environ["LLM_MODEL"],
            max_tokens=1024,
            messages=messages,
            tools=MATH_TOOLS,
            tool_choice="auto"
        )
        
        # Add AI response to conversation
        assistant_message = response.choices[0].message
        messages.append({
            "role": "assistant",
            "content": assistant_message.content or "",
            "tool_calls": assistant_message.tool_calls
        })
        
        # Check if we have tool calls
        if assistant_message.tool_calls:
            # Execute each tool
            tool_results = []
            for call in assistant_message.tool_calls:
                print(f"🔧 Using tool: {call.function.name}")
                try:
                    # Parse arguments
                    arguments = json.loads(call.function.arguments)
                    result = execute_math_tool(call.function.name, arguments)
                    tool_results.append({
                        "role": "tool",
                        "content": result,
                        "tool_call_id": call.id
                    })
                except json.JSONDecodeError as e:
                    error_result = f"Error parsing arguments: {str(e)}"
                    tool_results.append({
                        "role": "tool",
                        "content": error_result,
                        "tool_call_id": call.id
                    })
            
            # Add results to conversation
            messages.extend(tool_results)
        else:
            # No more tools, we're done
            if assistant_message.content:
                print(f"\n✅ Done: {assistant_message.content}")
            break

# Interactive CLI
if __name__ == "__main__":
    # Check for required environment variables
    if "API_KEY" not in os.environ:
        print("❌ Error: API_KEY environment variable not set")
        print("Please set it in your .env file or environment")
        exit(1)
    
    if "LLM_MODEL" not in os.environ:
        print("❌ Error: LLM_MODEL environment variable not set")
        print("Please set it in your .env file or environment")
        exit(1)
    
    print("🧮 Math Agent with LLM")
    print("Available tools: add, subtract, multiply, divide, power")
    print("Example: 'What is 15 + 27?' or 'Calculate 5 to the power of 3'")
    print()
    
    while True:
        try:
            prompt = input("\n💬 What would you like me to calculate? (or 'quit' to exit)\n> ")
            if prompt.lower() == 'quit':
                break
            run_agent(prompt)
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            print("Please check your API key and model configuration.")
