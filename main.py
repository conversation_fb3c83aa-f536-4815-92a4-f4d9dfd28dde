import os
import json
import logging
import time
from dotenv import load_dotenv
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("agent_workflow.log")
    ]
)
logger = logging.getLogger("main")

# Import our agents
from task_manager_agent import TaskManager
from executor_agent import ExecutorAgent

# Load environment variables
load_dotenv()

def print_separator(title=None):
    """Print a separator line with optional title"""
    width = 80
    if title:
        padding = (width - len(title) - 4) // 2
        print("\n" + "=" * padding + f" {title} " + "=" * padding + "\n")
    else:
        print("\n" + "=" * width + "\n")

def print_todo_list(todo_list: List[Dict[str, Any]]):
    """Print a formatted todo list"""
    print_separator("TODO LIST")
    
    for item in todo_list:
        status_symbol = "✅" if item["status"] == "completed" else "❌" if item["status"] == "failed" else "⏳" if item["status"] == "in_progress" else "⏸️"
        print(f"{status_symbol} {item['description']}")
        
        for subtask in item.get("subtasks", []):
            status_symbol = "✅" if subtask["status"] == "completed" else "❌" if subtask["status"] == "failed" else "⏳" if subtask["status"] == "in_progress" else "⏸️"
            print(f"  └─ {status_symbol} {subtask['description']}")
    
    print()

def log_agent_action(agent_type: str, action: str, details: Dict[str, Any] = None):
    """Log an agent action with details"""
    logger.info(f"AGENT: {agent_type} | ACTION: {action}")
    if details:
        logger.info(f"DETAILS: {json.dumps(details, indent=2)}")
    
    # Also print to console with formatting
    print_separator(f"{agent_type.upper()} AGENT - {action.upper()}")
    print(f"🤖 {agent_type.capitalize()} Agent: {action}")
    if details:
        for key, value in details.items():
            if isinstance(value, dict) or isinstance(value, list):
                print(f"  {key}:")
                print(f"  {json.dumps(value, indent=4)}")
            else:
                print(f"  {key}: {value}")
    print()

def process_query(query: str):
    """
    Process a user query through the task manager and executor workflow
    """
    # Check for required environment variables
    if "API_KEY" not in os.environ:
        logger.error("API_KEY environment variable not set")
        print("❌ Error: API_KEY environment variable not set")
        print("Please set it in your .env file or environment")
        return
    
    if "LLM_MODEL" not in os.environ:
        logger.error("LLM_MODEL environment variable not set")
        print("❌ Error: LLM_MODEL environment variable not set")
        print("Please set it in your .env file or environment")
        return
    
    api_key = os.environ["API_KEY"]
    model = os.environ["LLM_MODEL"]
    
    # Initialize agents
    task_manager = TaskManager(api_key=api_key, model=model)
    executor = ExecutorAgent(api_key=api_key, model=model)
    
    # Step 1: Task Manager evaluates the query
    log_agent_action("task manager", "evaluating query", {"query": query})
    task_info = task_manager.process_query(query)
    
    # Log the initial todo list
    todo_list = task_manager.get_todo_list()
    task_manager.log_todo_list()
    print_todo_list(todo_list)
    
    # Step 2: Process tasks until completion
    is_complex = task_info["is_complex"]
    
    if is_complex:
        log_agent_action("task manager", "complex query detected", {
            "total_subtasks": task_info["total_subtasks"]
        })
        
        # Process each subtask sequentially
        current_task_id = task_info["current_task_id"]
        
        while current_task_id:
            # Get current task details
            current_task = task_manager.get_task_status(current_task_id)
            
            # Get tools for this task
            tools = task_manager.get_task_tools(current_task_id)
            
            # Log task execution
            log_agent_action("task manager", "delegating task to executor", {
                "task_id": current_task_id,
                "description": current_task["description"],
                "tool_count": len(tools)
            })
            
            # Execute task
            log_agent_action("executor", "executing task", {
                "task_id": current_task_id,
                "description": current_task["description"],
                "available_tools": [tool["function"]["name"] for tool in tools]
            })
            
            # Execute the task
            result = executor.execute_task(current_task["description"], tools)
            
            # Log execution result
            log_agent_action("executor", "task completed", {
                "task_id": current_task_id,
                "result": result
            })
            
            # Update task status in task manager
            log_agent_action("task manager", "updating task status", {
                "task_id": current_task_id,
                "status": "completed"
            })
            
            next_task_info = task_manager.update_task_status(current_task_id, result=result["result"])
            
            # Update todo list
            todo_list = task_manager.get_todo_list()
            task_manager.log_todo_list()
            print_todo_list(todo_list)
            
            # Check if we have more tasks
            if next_task_info["status"] == "next_task":
                current_task_id = next_task_info["task_id"]
                log_agent_action("task manager", "moving to next task", {
                    "task_id": current_task_id,
                    "description": next_task_info["description"]
                })
            elif next_task_info["status"] == "all_completed":
                log_agent_action("task manager", "all tasks completed", {
                    "parent_task_id": next_task_info["parent_task_id"]
                })
                current_task_id = None
                
                # Print final results
                print_separator("FINAL RESULTS")
                for i, result in enumerate(next_task_info["results"]):
                    print(f"Subtask {i+1}: {result['description']}")
                    print(f"Result: {result['result']}")
                    print()
            else:
                current_task_id = None
    else:
        # Simple query with a single task
        log_agent_action("task manager", "simple query detected")
        
        current_task_id = task_info["current_task_id"]
        current_task = task_manager.get_task_status(current_task_id)
        
        # Get tools for this task
        tools = task_manager.get_task_tools(current_task_id)
        
        # Log task execution
        log_agent_action("task manager", "delegating task to executor", {
            "task_id": current_task_id,
            "description": current_task["description"]
        })
        
        # Execute task
        log_agent_action("executor", "executing task", {
            "task_id": current_task_id,
            "description": current_task["description"],
            "available_tools": [tool["function"]["name"] for tool in tools]
        })
        
        # Execute the task
        result = executor.execute_task(current_task["description"], tools)
        
        # Log execution result
        log_agent_action("executor", "task completed", {
            "task_id": current_task_id,
            "result": result
        })
        
        # Update task status in task manager
        log_agent_action("task manager", "updating task status", {
            "task_id": current_task_id,
            "status": "completed"
        })
        
        task_manager.update_task_status(current_task_id, result=result["result"])
        
        # Update todo list
        todo_list = task_manager.get_todo_list()
        task_manager.log_todo_list()
        print_todo_list(todo_list)
        
        # Print final result
        print_separator("FINAL RESULT")
        print(result["result"])
        print()
    
    log_agent_action("main", "workflow completed", {"query": query})

def main():
    """Main entry point for the agent workflow"""
    print_separator("AGENT WORKFLOW SYSTEM")
    print("This system uses a Task Manager and Executor Agent workflow.")
    print("The Task Manager evaluates queries and breaks them down into tasks.")
    print("The Executor Agent executes tasks with specific tools.")
    print()
    print("Available tool categories:")
    print("- Math tools: add, subtract, multiply, divide, power")
    print("- Text tools: text_analyze, text_translate")
    print("- Search tools: web_search, image_search")
    print("- Database tools: db_query, db_update")
    print()
    
    while True:
        try:
            query = input("\n💬 Enter your query (or 'quit' to exit):\n> ")
            if query.lower() in ['quit', 'exit', 'q']:
                print("\n👋 Goodbye!")
                break
            
            process_query(query)
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            logger.exception("Error processing query")
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
