import os
import json
import logging
from typing import List, Dict, Any, Optional
from openai import OpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("executor")

# Import tool execution functions
from tools import execute_math_tool, execute_text_tool, execute_search_tool, execute_database_tool

class ExecutorAgent:
    """
    Executor Agent that executes tasks with specific tools
    This agent has limited access to tools, as determined by the task manager
    """
    def __init__(self, api_key: str, model: str):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://api.openai.com/v1"
        )
        self.model = model
        logger.info(f"Executor Agent initialized with model: {model}")
    
    def execute_task(self, task_description: str, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Execute a task with the provided tools
        Returns the result of the task execution
        """
        logger.info(f"Executing task: {task_description}")
        logger.info(f"Available tools: {[tool['function']['name'] for tool in tools]}")
        
        # Create system message with task description
        system_message = f"""
        You are a task execution assistant. Your job is to:
        1. Understand the task: "{task_description}"
        2. Use the provided tools to complete the task
        3. Return a clear, concise result
        
        You only have access to the tools provided for this specific task.
        """
        
        messages = [{"role": "system", "content": system_message}]
        
        # Execute the task using the LLM with provided tools
        while True:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=tools,
                tool_choice="auto"
            )
            
            assistant_message = response.choices[0].message
            
            # Add assistant message to conversation
            messages.append({
                "role": "assistant",
                "content": assistant_message.content or "",
                "tool_calls": assistant_message.tool_calls
            })
            
            # Check if we have tool calls
            if assistant_message.tool_calls:
                # Execute each tool
                tool_results = []
                for call in assistant_message.tool_calls:
                    tool_name = call.function.name
                    logger.info(f"Executor using tool: {tool_name}")
                    
                    try:
                        # Parse arguments
                        arguments = json.loads(call.function.arguments)
                        
                        # Determine tool category and execute
                        result = self._execute_tool(tool_name, arguments)
                        
                        tool_results.append({
                            "role": "tool",
                            "content": json.dumps(result) if isinstance(result, dict) else result,
                            "tool_call_id": call.id
                        })
                        
                        logger.info(f"Tool {tool_name} executed successfully")
                    except json.JSONDecodeError as e:
                        error_result = f"Error parsing arguments: {str(e)}"
                        tool_results.append({
                            "role": "tool",
                            "content": error_result,
                            "tool_call_id": call.id
                        })
                        logger.error(f"Error parsing arguments for tool {tool_name}: {str(e)}")
                    except Exception as e:
                        error_result = f"Error executing tool: {str(e)}"
                        tool_results.append({
                            "role": "tool",
                            "content": error_result,
                            "tool_call_id": call.id
                        })
                        logger.error(f"Error executing tool {tool_name}: {str(e)}")
                
                # Add results to conversation
                messages.extend(tool_results)
            else:
                # No more tools, we're done
                if assistant_message.content:
                    logger.info(f"Task completed: {task_description}")
                    return {
                        "status": "completed",
                        "result": assistant_message.content
                    }
                else:
                    logger.error("Task completed but no result was provided")
                    return {
                        "status": "error",
                        "error": "No result was provided by the assistant"
                    }
    
    def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Execute a tool based on its name"""
        # Math tools
        if tool_name in ["add", "subtract", "multiply", "divide", "power"]:
            return execute_math_tool(tool_name, arguments)
        
        # Text tools
        elif tool_name in ["text_analyze", "text_translate"]:
            return execute_text_tool(tool_name, arguments)
        
        # Search tools
        elif tool_name in ["web_search", "image_search"]:
            return execute_search_tool(tool_name, arguments)
        
        # Database tools
        elif tool_name in ["db_query", "db_update"]:
            return execute_database_tool(tool_name, arguments)
        
        else:
            error_msg = f"Unknown tool: {tool_name}"
            logger.error(error_msg)
            return error_msg