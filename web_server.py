import os
import json
import async<PERSON>
import logging
from typing import Dict, Any, List
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import uvicorn

# Import our agents
from task_manager_agent import TaskManager
from executor_agent import ExecutorAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("web_server")

app = FastAPI(title="Custom Agent Web Interface")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Global variables to store agent instances
task_manager = None
executor = None

def initialize_agents():
    """Initialize the agents with API key and model"""
    global task_manager, executor
    
    if "API_KEY" not in os.environ:
        logger.error("API_KEY environment variable not set")
        return False
    
    if "LLM_MODEL" not in os.environ:
        logger.error("LLM_MODEL environment variable not set")
        return False
    
    api_key = os.environ["API_KEY"]
    model = os.environ["LLM_MODEL"]
    
    task_manager = TaskManager(api_key=api_key, model=model)
    executor = ExecutorAgent(api_key=api_key, model=model)
    
    logger.info("Agents initialized successfully")
    return True

@app.on_event("startup")
async def startup_event():
    """Initialize agents on startup"""
    if not initialize_agents():
        logger.error("Failed to initialize agents")

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the main HTML page"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Custom Agent Interface</title>
        <link rel="stylesheet" href="/static/style.css">
    </head>
    <body>
        <div class="container">
            <header>
                <h1>🤖 Custom Agent Interface</h1>
                <p>Task Manager & Executor Agent System</p>
            </header>
            
            <div class="main-content">
                <div class="chat-section">
                    <div id="chat-messages" class="chat-messages"></div>
                    
                    <div class="input-section">
                        <div class="input-container">
                            <input type="text" id="query-input" placeholder="Enter your query..." />
                            <button id="send-button">Send</button>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar">
                    <div class="todo-section">
                        <h3>📋 Todo List</h3>
                        <div id="todo-list" class="todo-list">
                            <p class="empty-state">No tasks yet</p>
                        </div>
                    </div>
                    
                    <div class="status-section">
                        <h3>📊 Status</h3>
                        <div id="status-info" class="status-info">
                            <span class="status-indicator ready">Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="/static/app.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

async def process_query_stream(query: str):
    """Process query and yield real-time updates"""
    global task_manager, executor
    
    if not task_manager or not executor:
        yield f"data: {json.dumps({'type': 'error', 'message': 'Agents not initialized'})}\n\n"
        return
    
    try:
        # Step 1: Task Manager evaluates the query
        yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'evaluating_query', 'data': {'query': query}})}\n\n"
        
        task_info = task_manager.process_query(query)
        
        # Send initial todo list
        todo_list = task_manager.get_todo_list()
        yield f"data: {json.dumps({'type': 'todo_update', 'data': todo_list})}\n\n"
        
        is_complex = task_info["is_complex"]
        
        if is_complex:
            yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'complex_query_detected', 'data': {'total_subtasks': task_info['total_subtasks']}})}\n\n"
            
            # Process subtasks
            while task_manager.current_task_id:
                current_task_id = task_manager.current_task_id
                current_task = task_manager.get_task_status(current_task_id)
                
                yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'starting_task', 'data': {'task_id': current_task_id, 'description': current_task['description']}})}\n\n"
                
                # Get tools for this task
                tools = task_manager.get_task_tools(current_task_id)
                yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'providing_tools', 'data': {'task_id': current_task_id, 'tool_count': len(tools)}})}\n\n"
                
                # Execute the task
                yield f"data: {json.dumps({'type': 'log', 'agent': 'executor', 'action': 'executing_task', 'data': {'task_id': current_task_id}})}\n\n"
                
                result = executor.execute_task(current_task["description"], tools)
                
                yield f"data: {json.dumps({'type': 'log', 'agent': 'executor', 'action': 'task_completed', 'data': {'task_id': current_task_id, 'result': result}})}\n\n"
                
                # Update task status
                yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'updating_task_status', 'data': {'task_id': current_task_id, 'status': 'completed'}})}\n\n"
                
                next_task_info = task_manager.update_task_status(current_task_id, result=result["result"])
                
                # Update todo list
                todo_list = task_manager.get_todo_list()
                yield f"data: {json.dumps({'type': 'todo_update', 'data': todo_list})}\n\n"
                
                # Check if we're done
                if not task_manager.current_task_id:
                    break
            
            # Get final result
            parent_task = task_manager.get_task_status(task_info["parent_task_id"])
            final_result = parent_task.get("result", "Complex task completed")
            
        else:
            # Simple task
            yield f"data: {json.dumps({'type': 'log', 'agent': 'task_manager', 'action': 'simple_query_detected', 'data': {}})}\n\n"
            
            current_task_id = task_info["current_task_id"]
            current_task = task_info["current_task"]
            
            # Get tools and execute
            tools = task_manager.get_task_tools(current_task_id)
            yield f"data: {json.dumps({'type': 'log', 'agent': 'executor', 'action': 'executing_task', 'data': {'task_id': current_task_id}})}\n\n"
            
            result = executor.execute_task(current_task["description"], tools)
            
            yield f"data: {json.dumps({'type': 'log', 'agent': 'executor', 'action': 'task_completed', 'data': {'task_id': current_task_id, 'result': result}})}\n\n"
            
            task_manager.update_task_status(current_task_id, result=result["result"])
            
            # Update todo list
            todo_list = task_manager.get_todo_list()
            yield f"data: {json.dumps({'type': 'todo_update', 'data': todo_list})}\n\n"
            
            final_result = result["result"]
        
        # Send final result
        yield f"data: {json.dumps({'type': 'final_result', 'data': final_result})}\n\n"
        yield f"data: {json.dumps({'type': 'workflow_completed', 'data': {'query': query}})}\n\n"
        
    except Exception as e:
        logger.exception("Error processing query")
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

@app.post("/query")
async def process_query(request: Request):
    """Process a query with Server-Sent Events"""
    data = await request.json()
    query = data.get("query", "")
    
    if not query:
        return {"error": "No query provided"}
    
    return StreamingResponse(
        process_query_stream(query),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.get("/status")
async def get_status():
    """Get current system status"""
    global task_manager, executor
    
    if not task_manager or not executor:
        return {"status": "not_initialized", "agents": False}
    
    current_task_id = task_manager.current_task_id if task_manager else None
    todo_list = task_manager.get_todo_list() if task_manager else []
    
    return {
        "status": "ready",
        "agents": True,
        "current_task_id": current_task_id,
        "todo_list": todo_list
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
