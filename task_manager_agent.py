import os
import json
import logging
import uuid
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional, Tuple
from openai import OpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("task_manager")

# Import tools
from tools import TOOL_CATEGORIES, get_tools_by_categories

class TaskStatus(Enum):
    """Enum for task status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class Task:
    """Represents a task to be executed"""
    def __init__(
        self, 
        task_id: str, 
        description: str, 
        tool_categories: List[str],
        parent_id: Optional[str] = None
    ):
        self.task_id = task_id
        self.description = description
        self.tool_categories = tool_categories
        self.parent_id = parent_id
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.result = None
        self.error = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary"""
        return {
            "task_id": self.task_id,
            "description": self.description,
            "tool_categories": self.tool_categories,
            "parent_id": self.parent_id,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error
        }
    
    def start(self):
        """Mark task as in progress"""
        self.status = TaskStatus.IN_PROGRESS
        self.started_at = datetime.now()
        logger.info(f"Task {self.task_id} started: {self.description}")
    
    def complete(self, result: Any):
        """Mark task as completed with result"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
        logger.info(f"Task {self.task_id} completed: {self.description}")
    
    def fail(self, error: str):
        """Mark task as failed with error"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error = error
        logger.error(f"Task {self.task_id} failed: {self.description} - Error: {error}")

class TaskManager:
    """Task Manager Agent that evaluates queries and manages tasks"""
    def __init__(self, api_key: str, model: str):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://api.openai.com/v1"
        )
        self.model = model
        self.tasks: Dict[str, Task] = {}
        self.current_task_id = None
        logger.info(f"Task Manager initialized with model: {model}")
    
    def _generate_task_id(self) -> str:
        """Generate a unique task ID"""
        return str(uuid.uuid4())
    
    def evaluate_query(self, query: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Evaluate if a query is simple or complex
        Returns:
        - is_complex: True if the query requires multiple steps, False otherwise
        - subtasks: List of subtask descriptions if complex, or a single task if simple
        """
        logger.info(f"Evaluating query: {query}")
        
        # Use LLM to evaluate query complexity and generate subtasks if needed
        messages = [
            {"role": "system", "content": """
            You are a task planning assistant. Your job is to:
            1. Determine if a user query is simple (can be done in one step with 1-2 tools) or complex (requires multiple steps)
            2. If complex, break it down into logical subtasks
            3. For each subtask, specify which tool categories are needed (math, text, search, database)
            
            Respond with a JSON object with the following structure:
            {
                "is_complex": true/false,
                "subtasks": [
                    {
                        "description": "subtask description",
                        "tool_categories": ["category1", "category2"]
                    }
                ]
            }
            
            For simple tasks, still include one subtask in the list.
            """},
            {"role": "user", "content": query}
        ]
        
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            response_format={"type": "json_object"}
        )
        
        result = json.loads(response.choices[0].message.content)
        logger.info(f"Query evaluation result: {result}")
        
        return result["is_complex"], result["subtasks"]
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Process a user query
        1. Evaluate if it's simple or complex
        2. Create tasks accordingly
        3. Return the task information
        """
        logger.info(f"Processing query: {query}")
        
        # Evaluate query complexity
        is_complex, subtasks = self.evaluate_query(query)
        
        if is_complex:
            logger.info(f"Complex query detected with {len(subtasks)} subtasks")
            
            # Create a parent task for the overall query
            parent_id = self._generate_task_id()
            parent_task = Task(
                task_id=parent_id,
                description=query,
                tool_categories=["all"]  # Parent task has access to all tools
            )
            self.tasks[parent_id] = parent_task
            
            # Create subtasks
            for i, subtask_info in enumerate(subtasks):
                task_id = self._generate_task_id()
                task = Task(
                    task_id=task_id,
                    description=subtask_info["description"],
                    tool_categories=subtask_info["tool_categories"],
                    parent_id=parent_id
                )
                self.tasks[task_id] = task
                logger.info(f"Created subtask {i+1}/{len(subtasks)}: {task.description}")
            
            # Start the first subtask
            first_subtask = next(task for task in self.tasks.values() 
                               if task.parent_id == parent_id and task.status == TaskStatus.PENDING)
            first_subtask.start()
            self.current_task_id = first_subtask.task_id
            
            return {
                "query": query,
                "is_complex": True,
                "parent_task_id": parent_id,
                "current_task_id": first_subtask.task_id,
                "total_subtasks": len(subtasks),
                "current_task": first_subtask.to_dict()
            }
        else:
            logger.info("Simple query detected")
            
            # Create a single task
            task_id = self._generate_task_id()
            subtask_info = subtasks[0]  # There should be only one subtask for simple queries
            task = Task(
                task_id=task_id,
                description=subtask_info["description"],
                tool_categories=subtask_info["tool_categories"]
            )
            self.tasks[task_id] = task
            task.start()
            self.current_task_id = task_id
            
            return {
                "query": query,
                "is_complex": False,
                "current_task_id": task_id,
                "current_task": task.to_dict()
            }
    
    def get_task_tools(self, task_id: str) -> List[Dict[str, Any]]:
        """Get the tools needed for a specific task"""
        if task_id not in self.tasks:
            logger.error(f"Task {task_id} not found")
            return []
        
        task = self.tasks[task_id]
        tools = get_tools_by_categories(task.tool_categories)
        logger.info(f"Providing {len(tools)} tools for task {task_id}")
        return tools
    
    def update_task_status(self, task_id: str, result: Any = None, error: str = None) -> Dict[str, Any]:
        """
        Update the status of a task
        Returns information about the next task if applicable
        """
        if task_id not in self.tasks:
            logger.error(f"Task {task_id} not found")
            return {"error": f"Task {task_id} not found"}
        
        task = self.tasks[task_id]
        
        if error:
            task.fail(error)
            return {
                "status": "failed",
                "task_id": task_id,
                "error": error
            }
        else:
            task.complete(result)
            
            # If this was a subtask, check if there are more subtasks
            if task.parent_id:
                parent_id = task.parent_id
                
                # Find the next pending subtask
                next_task = None
                for t in self.tasks.values():
                    if t.parent_id == parent_id and t.status == TaskStatus.PENDING:
                        next_task = t
                        break
                
                if next_task:
                    # Start the next subtask
                    next_task.start()
                    self.current_task_id = next_task.task_id
                    
                    return {
                        "status": "next_task",
                        "task_id": next_task.task_id,
                        "description": next_task.description,
                        "task": next_task.to_dict()
                    }
                else:
                    # All subtasks completed, mark parent task as completed
                    parent_task = self.tasks[parent_id]
                    
                    # Collect results from all subtasks
                    subtask_results = []
                    for t in self.tasks.values():
                        if t.parent_id == parent_id and t.status == TaskStatus.COMPLETED:
                            subtask_results.append({
                                "description": t.description,
                                "result": t.result
                            })
                    
                    parent_task.complete(subtask_results)
                    self.current_task_id = None
                    
                    return {
                        "status": "all_completed",
                        "parent_task_id": parent_id,
                        "results": subtask_results
                    }
            else:
                # This was a simple task, we're done
                self.current_task_id = None
                return {
                    "status": "completed",
                    "task_id": task_id,
                    "result": result
                }
    
    def get_task_status(self, task_id: str = None) -> Dict[str, Any]:
        """Get the status of a specific task or all tasks"""
        if task_id:
            if task_id not in self.tasks:
                logger.error(f"Task {task_id} not found")
                return {"error": f"Task {task_id} not found"}
            
            return self.tasks[task_id].to_dict()
        else:
            # Return status of all tasks
            return {
                "current_task_id": self.current_task_id,
                "tasks": {task_id: task.to_dict() for task_id, task in self.tasks.items()}
            }
    
    def get_todo_list(self) -> List[Dict[str, Any]]:
        """Get a formatted todo list of all tasks"""
        todo_list = []
        
        # First add parent tasks
        for task_id, task in self.tasks.items():
            if not task.parent_id:  # This is a parent task or simple task
                todo_item = {
                    "task_id": task_id,
                    "description": task.description,
                    "status": task.status.value,
                    "subtasks": []
                }
                
                # Add subtasks if any
                for subtask_id, subtask in self.tasks.items():
                    if subtask.parent_id == task_id:
                        todo_item["subtasks"].append({
                            "task_id": subtask_id,
                            "description": subtask.description,
                            "status": subtask.status.value
                        })
                
                todo_list.append(todo_item)
        
        return todo_list
    
    def log_todo_list(self):
        """Log the current todo list"""
        todo_list = self.get_todo_list()
        logger.info("Current TODO List:")
        
        for item in todo_list:
            logger.info(f"- {item['description']} [{item['status']}]")
            for subtask in item.get("subtasks", []):
                logger.info(f"  * {subtask['description']} [{subtask['status']}]")