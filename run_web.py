#!/usr/bin/env python3
"""
Simple script to run the web server
Usage: uv run python run_web.py
"""

import uvicorn
from web_server import app

if __name__ == "__main__":
    print("🚀 Starting Custom Agent Web Interface...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🔧 Make sure your .env file has API_KEY and LLM_MODEL set")
    print("⏹️  Press Ctrl+C to stop the server")
    print()
    
    uvicorn.run(
        "web_server:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=True  # Enable auto-reload for development
    )
