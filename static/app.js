class AgentInterface {
    constructor() {
        this.chatMessages = document.getElementById('chat-messages');
        this.queryInput = document.getElementById('query-input');
        this.sendButton = document.getElementById('send-button');
        this.todoList = document.getElementById('todo-list');
        this.statusInfo = document.getElementById('status-info');
        
        this.isProcessing = false;
        this.eventSource = null;
        
        this.init();
    }
    
    init() {
        // Event listeners
        this.sendButton.addEventListener('click', () => this.sendQuery());
        this.queryInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendQuery();
            }
        });
        
        // Load initial status
        this.loadStatus();
        
        // Auto-focus input
        this.queryInput.focus();
    }
    
    async loadStatus() {
        try {
            const response = await fetch('/status');
            const status = await response.json();
            this.updateStatus(status);
            
            if (status.todo_list && status.todo_list.length > 0) {
                this.updateTodoList(status.todo_list);
            }
        } catch (error) {
            console.error('Failed to load status:', error);
            this.updateStatus({ status: 'error', agents: false });
        }
    }
    
    async sendQuery() {
        const query = this.queryInput.value.trim();
        if (!query || this.isProcessing) return;
        
        this.isProcessing = true;
        this.updateUI(true);
        
        // Add user message
        this.addMessage('user', query);
        this.queryInput.value = '';
        
        try {
            // Start SSE connection
            const response = await fetch('/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // Process SSE stream
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleSSEMessage(data);
                        } catch (e) {
                            console.error('Failed to parse SSE data:', e);
                        }
                    }
                }
            }
            
        } catch (error) {
            console.error('Error sending query:', error);
            this.addMessage('error', `Error: ${error.message}`);
        } finally {
            this.isProcessing = false;
            this.updateUI(false);
        }
    }
    
    handleSSEMessage(data) {
        switch (data.type) {
            case 'log':
                this.addLogMessage(data);
                break;
            case 'todo_update':
                this.updateTodoList(data.data);
                break;
            case 'final_result':
                this.addMessage('final-result', data.data);
                break;
            case 'workflow_completed':
                this.addMessage('log', '✅ Workflow completed successfully');
                break;
            case 'error':
                this.addMessage('error', data.message);
                break;
        }
    }
    
    addMessage(type, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        if (type === 'final-result') {
            messageDiv.innerHTML = `<strong>🎯 Final Result:</strong><br>${this.formatContent(content)}`;
        } else {
            messageDiv.innerHTML = this.formatContent(content);
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    addLogMessage(data) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message log';
        
        const agentBadge = `<span class="agent-badge ${data.agent}">${data.agent}</span>`;
        const action = data.action.replace(/_/g, ' ');
        const details = data.data ? this.formatLogDetails(data.data) : '';
        
        messageDiv.innerHTML = `${agentBadge}${action}${details}`;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    formatLogDetails(data) {
        if (!data || Object.keys(data).length === 0) return '';
        
        const details = Object.entries(data)
            .map(([key, value]) => {
                if (typeof value === 'object') {
                    return `${key}: ${JSON.stringify(value)}`;
                }
                return `${key}: ${value}`;
            })
            .join(', ');
        
        return ` - ${details}`;
    }
    
    formatContent(content) {
        if (typeof content === 'object') {
            return `<pre>${JSON.stringify(content, null, 2)}</pre>`;
        }
        return content.toString().replace(/\n/g, '<br>');
    }
    
    updateTodoList(todoData) {
        if (!todoData || todoData.length === 0) {
            this.todoList.innerHTML = '<p class="empty-state">No tasks yet</p>';
            return;
        }
        
        this.todoList.innerHTML = '';
        
        todoData.forEach(item => {
            const todoItem = document.createElement('div');
            todoItem.className = `todo-item ${item.status}`;
            
            let subtasksHtml = '';
            if (item.subtasks && item.subtasks.length > 0) {
                subtasksHtml = '<div class="subtasks">' +
                    item.subtasks.map(subtask => 
                        `<div class="subtask ${subtask.status}">
                            <div class="todo-item-description">${subtask.description}</div>
                            <div class="todo-item-status">${subtask.status}</div>
                        </div>`
                    ).join('') +
                    '</div>';
            }
            
            todoItem.innerHTML = `
                <div class="todo-item-description">${item.description}</div>
                <div class="todo-item-status">${item.status}</div>
                ${subtasksHtml}
            `;
            
            this.todoList.appendChild(todoItem);
        });
    }
    
    updateStatus(status) {
        const statusIndicator = this.statusInfo.querySelector('.status-indicator');
        
        if (status.agents && status.status === 'ready') {
            statusIndicator.className = 'status-indicator ready';
            statusIndicator.textContent = 'Ready';
        } else if (this.isProcessing) {
            statusIndicator.className = 'status-indicator processing';
            statusIndicator.textContent = 'Processing';
        } else {
            statusIndicator.className = 'status-indicator error';
            statusIndicator.textContent = 'Error';
        }
    }
    
    updateUI(processing) {
        this.sendButton.disabled = processing;
        this.queryInput.disabled = processing;
        
        if (processing) {
            this.sendButton.textContent = 'Processing...';
            this.updateStatus({ status: 'processing' });
        } else {
            this.sendButton.textContent = 'Send';
            this.loadStatus(); // Refresh status
        }
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// Initialize the interface when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new AgentInterface();
});

// Add some helpful keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to send message
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        document.getElementById('send-button').click();
    }
    
    // Escape to clear input
    if (e.key === 'Escape') {
        document.getElementById('query-input').value = '';
        document.getElementById('query-input').focus();
    }
});

// Add some utility functions for better UX
window.addEventListener('beforeunload', (e) => {
    const agentInterface = window.agentInterface;
    if (agentInterface && agentInterface.isProcessing) {
        e.preventDefault();
        e.returnValue = 'A query is currently being processed. Are you sure you want to leave?';
    }
});
