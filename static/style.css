* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    flex: 1;
    height: calc(100vh - 200px);
}

.chat-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 8px;
    max-width: 80%;
    word-wrap: break-word;
}

.message.user {
    background: #007bff;
    color: white;
    margin-left: auto;
}

.message.assistant {
    background: white;
    border: 1px solid #e9ecef;
}

.message.log {
    background: #f8f9fa;
    border-left: 4px solid #6c757d;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    color: #495057;
}

.message.error {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.message.final-result {
    background: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
    font-weight: 500;
}

.input-section {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.input-container {
    display: flex;
    gap: 10px;
}

#query-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.2s;
}

#query-input:focus {
    border-color: #007bff;
}

#send-button {
    padding: 12px 24px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

#send-button:hover {
    background: #0056b3;
}

#send-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.todo-section, .status-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    padding: 20px;
}

.todo-section {
    flex: 1;
}

.todo-section h3, .status-section h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.1rem;
}

.todo-list {
    max-height: 400px;
    overflow-y: auto;
}

.todo-item {
    margin-bottom: 12px;
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid #6c757d;
}

.todo-item.pending {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.todo-item.in_progress {
    border-left-color: #007bff;
    background: #cce7ff;
}

.todo-item.completed {
    border-left-color: #28a745;
    background: #d4edda;
}

.todo-item.failed {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.todo-item-description {
    font-weight: 500;
    margin-bottom: 5px;
}

.todo-item-status {
    font-size: 0.85rem;
    text-transform: uppercase;
    font-weight: 600;
    opacity: 0.8;
}

.subtasks {
    margin-top: 8px;
    margin-left: 15px;
}

.subtask {
    margin-bottom: 6px;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    border-left: 2px solid #dee2e6;
}

.subtask.pending {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.subtask.in_progress {
    border-left-color: #007bff;
    background: #f0f8ff;
}

.subtask.completed {
    border-left-color: #28a745;
    background: #f0fff4;
}

.subtask.failed {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-indicator.ready {
    background: #d4edda;
    color: #155724;
}

.status-indicator.processing {
    background: #cce7ff;
    color: #004085;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

.empty-state {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

.agent-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-right: 8px;
}

.agent-badge.task_manager {
    background: #e3f2fd;
    color: #1565c0;
}

.agent-badge.executor {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .sidebar {
        flex-direction: row;
    }
    
    .todo-section, .status-section {
        flex: 1;
    }
    
    header h1 {
        font-size: 2rem;
    }
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar,
.todo-list::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.todo-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
.todo-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.todo-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
