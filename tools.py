import math
import json
import logging
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("tools")

# ===== MATH TOOLS =====
MATH_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "add",
            "description": "Add two or more numbers together",
            "parameters": {
                "type": "object",
                "properties": {
                    "numbers": {
                        "type": "array",
                        "items": {"type": "number"},
                        "description": "List of numbers to add together"
                    }
                },
                "required": ["numbers"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "subtract",
            "description": "Subtract the second number from the first number",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "First number (minuend)"
                    },
                    "b": {
                        "type": "number", 
                        "description": "Second number (subtrahend)"
                    }
                },
                "required": ["a", "b"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "multiply",
            "description": "Multiply two or more numbers together",
            "parameters": {
                "type": "object",
                "properties": {
                    "numbers": {
                        "type": "array",
                        "items": {"type": "number"},
                        "description": "List of numbers to multiply together"
                    }
                },
                "required": ["numbers"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "divide",
            "description": "Divide the first number by the second number",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "number",
                        "description": "First number (dividend)"
                    },
                    "b": {
                        "type": "number",
                        "description": "Second number (divisor)"
                    }
                },
                "required": ["a", "b"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "power",
            "description": "Raise a number to a given power",
            "parameters": {
                "type": "object",
                "properties": {
                    "base": {
                        "type": "number",
                        "description": "The base number"
                    },
                    "exponent": {
                        "type": "number",
                        "description": "The exponent to raise the base to"
                    }
                },
                "required": ["base", "exponent"]
            }
        }
    }
]

# ===== TEXT TOOLS =====
TEXT_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "text_analyze",
            "description": "Analyze text for sentiment, word count, and character count",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to analyze"
                    }
                },
                "required": ["text"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "text_translate",
            "description": "Simulate translating text to another language",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "The text to translate"
                    },
                    "target_language": {
                        "type": "string",
                        "description": "The target language code (e.g., 'es', 'fr', 'de')"
                    }
                },
                "required": ["text", "target_language"]
            }
        }
    }
]

# ===== SEARCH TOOLS =====
SEARCH_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Simulate searching the web for information",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query"
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "image_search",
            "description": "Simulate searching for images",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The image search query"
                    }
                },
                "required": ["query"]
            }
        }
    }
]

# ===== DATABASE TOOLS =====
DATABASE_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "db_query",
            "description": "Simulate querying a database",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The SQL query to execute"
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "db_update",
            "description": "Simulate updating a database",
            "parameters": {
                "type": "object",
                "properties": {
                    "table": {
                        "type": "string",
                        "description": "The table to update"
                    },
                    "data": {
                        "type": "object",
                        "description": "The data to update (key-value pairs)"
                    },
                    "condition": {
                        "type": "string",
                        "description": "The WHERE condition for the update"
                    }
                },
                "required": ["table", "data", "condition"]
            }
        }
    }
]

# Combine all tools
ALL_TOOLS = MATH_TOOLS + TEXT_TOOLS + SEARCH_TOOLS + DATABASE_TOOLS

# Tool categories for easy access
TOOL_CATEGORIES = {
    "math": MATH_TOOLS,
    "text": TEXT_TOOLS,
    "search": SEARCH_TOOLS,
    "database": DATABASE_TOOLS,
    "all": ALL_TOOLS
}

def execute_math_tool(name, arguments):
    """Execute a math tool and return the result"""
    logger.info(f"Executing math tool: {name} with arguments: {arguments}")
    try:
        if name == "add":
            numbers = arguments.get("numbers", [])
            if not numbers:
                return "Error: No numbers provided for addition"
            result = sum(numbers)
            return f"Result: {result} (sum of {numbers})"
        
        elif name == "subtract":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                return "Error: Both numbers are required for subtraction"
            result = a - b
            return f"Result: {result} ({a} - {b})"
        
        elif name == "multiply":
            numbers = arguments.get("numbers", [])
            if not numbers:
                return "Error: No numbers provided for multiplication"
            result = 1
            for num in numbers:
                result *= num
            return f"Result: {result} (product of {numbers})"
        
        elif name == "divide":
            a = arguments.get("a")
            b = arguments.get("b")
            if a is None or b is None:
                return "Error: Both numbers are required for division"
            if b == 0:
                return "Error: Cannot divide by zero"
            result = a / b
            return f"Result: {result} ({a} ÷ {b})"
        
        elif name == "power":
            base = arguments.get("base")
            exponent = arguments.get("exponent")
            if base is None or exponent is None:
                return "Error: Both base and exponent are required"
            result = math.pow(base, exponent)
            return f"Result: {result} ({base}^{exponent})"
        
        else:
            return f"Error: Unknown math tool '{name}'"
    
    except Exception as e:
        logger.error(f"Error executing math tool {name}: {str(e)}")
        return f"Error executing {name}: {str(e)}"

def execute_text_tool(name, arguments):
    """Execute a text tool and return the result"""
    logger.info(f"Executing text tool: {name} with arguments: {arguments}")
    try:
        if name == "text_analyze":
            text = arguments.get("text", "")
            if not text:
                return "Error: No text provided for analysis"
            
            # Simulate processing time
            time.sleep(0.5)
            
            word_count = len(text.split())
            char_count = len(text)
            
            # Simple sentiment analysis (dummy)
            positive_words = ["good", "great", "excellent", "happy", "positive"]
            negative_words = ["bad", "terrible", "sad", "negative", "poor"]
            
            sentiment = "neutral"
            text_lower = text.lower()
            
            pos_count = sum(1 for word in positive_words if word in text_lower)
            neg_count = sum(1 for word in negative_words if word in text_lower)
            
            if pos_count > neg_count:
                sentiment = "positive"
            elif neg_count > pos_count:
                sentiment = "negative"
            
            return {
                "word_count": word_count,
                "character_count": char_count,
                "sentiment": sentiment
            }
        
        elif name == "text_translate":
            text = arguments.get("text", "")
            target_language = arguments.get("target_language", "")
            
            if not text:
                return "Error: No text provided for translation"
            if not target_language:
                return "Error: No target language specified"
            
            # Simulate processing time
            time.sleep(1)
            
            # Dummy translation (just append language code)
            return f"[Translated to {target_language}]: {text} (dummy translation)"
        
        else:
            return f"Error: Unknown text tool '{name}'"
    
    except Exception as e:
        logger.error(f"Error executing text tool {name}: {str(e)}")
        return f"Error executing {name}: {str(e)}"

def execute_search_tool(name, arguments):
    """Execute a search tool and return the result"""
    logger.info(f"Executing search tool: {name} with arguments: {arguments}")
    try:
        if name == "web_search":
            query = arguments.get("query", "")
            if not query:
                return "Error: No query provided for web search"
            
            # Simulate search delay
            time.sleep(1.5)
            
            # Return dummy search results
            return {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "results": [
                    {"title": f"Result 1 for {query}", "url": f"https://example.com/1?q={query}"},
                    {"title": f"Result 2 for {query}", "url": f"https://example.com/2?q={query}"},
                    {"title": f"Result 3 for {query}", "url": f"https://example.com/3?q={query}"}
                ]
            }
        
        elif name == "image_search":
            query = arguments.get("query", "")
            if not query:
                return "Error: No query provided for image search"
            
            # Simulate search delay
            time.sleep(1.2)
            
            # Return dummy image search results
            return {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "results": [
                    {"title": f"Image 1 for {query}", "url": f"https://example.com/images/1?q={query}"},
                    {"title": f"Image 2 for {query}", "url": f"https://example.com/images/2?q={query}"},
                    {"title": f"Image 3 for {query}", "url": f"https://example.com/images/3?q={query}"}
                ]
            }
        
        else:
            return f"Error: Unknown search tool '{name}'"
    
    except Exception as e:
        logger.error(f"Error executing search tool {name}: {str(e)}")
        return f"Error executing {name}: {str(e)}"

def execute_database_tool(name, arguments):
    """Execute a database tool and return the result"""
    logger.info(f"Executing database tool: {name} with arguments: {arguments}")
    try:
        if name == "db_query":
            query = arguments.get("query", "")
            if not query:
                return "Error: No query provided for database query"
            
            # Simulate database query delay
            time.sleep(0.8)
            
            # Return dummy query results
            if "SELECT" in query.upper():
                return {
                    "query": query,
                    "timestamp": datetime.now().isoformat(),
                    "rows": [
                        {"id": 1, "name": "Sample 1", "value": 100},
                        {"id": 2, "name": "Sample 2", "value": 200},
                        {"id": 3, "name": "Sample 3", "value": 300}
                    ],
                    "row_count": 3
                }
            else:
                return {
                    "query": query,
                    "timestamp": datetime.now().isoformat(),
                    "message": "Query executed successfully",
                    "affected_rows": 5
                }
        
        elif name == "db_update":
            table = arguments.get("table", "")
            data = arguments.get("data", {})
            condition = arguments.get("condition", "")
            
            if not table:
                return "Error: No table specified for database update"
            if not data:
                return "Error: No data provided for database update"
            
            # Simulate database update delay
            time.sleep(1)
            
            # Return dummy update result
            return {
                "table": table,
                "data": data,
                "condition": condition,
                "timestamp": datetime.now().isoformat(),
                "message": "Update executed successfully",
                "affected_rows": 3
            }
        
        else:
            return f"Error: Unknown database tool '{name}'"
    
    except Exception as e:
        logger.error(f"Error executing database tool {name}: {str(e)}")
        return f"Error executing {name}: {str(e)}"

def execute_tool(category, name, arguments):
    """Execute a tool from any category"""
    logger.info(f"Tool execution request: category={category}, name={name}")
    
    if category == "math":
        return execute_math_tool(name, arguments)
    elif category == "text":
        return execute_text_tool(name, arguments)
    elif category == "search":
        return execute_search_tool(name, arguments)
    elif category == "database":
        return execute_database_tool(name, arguments)
    else:
        error_msg = f"Unknown tool category: {category}"
        logger.error(error_msg)
        return error_msg

def get_tools_by_categories(categories):
    """Get tools from specified categories"""
    tools = []
    for category in categories:
        if category in TOOL_CATEGORIES:
            tools.extend(TOOL_CATEGORIES[category])
    return tools